# دليل إعداد جدول الامتحان

## 📋 نظرة عامة

هذه الميزة تتيح لك إنشاء جدول امتحان حقيقي ومخصص بدلاً من استخدام الجدول الافتراضي. يمكنك تحديد التواريخ والفترات والحصص والقاعات بحرية كاملة.

## 🚀 كيفية الوصول للميزة

1. **افتح البرنامج الرئيسي**
2. **انقر على زر "📅 إعداد جدول الامتحان"** في شريط الأدوات العلوي
3. **ستفتح نافذة الإعداد الجديدة**

## ⚙️ خطوات الإعداد

### 1️⃣ إدخال تواريخ الامتحان
```
📅 أدخل كل تاريخ في سطر منفصل
مثال:
2024-01-15
2024-01-16
2024-01-17
2024-01-18
```

**ملاحظات:**
- يمكنك إدخال أي عدد من التواريخ
- استخدم تنسيق YYYY-MM-DD
- كل تاريخ سيصبح "يوم امتحان" منفصل

### 2️⃣ اختيار عدد الفترات
**خيارات متاحة:**
- ⭕ **فترة واحدة**: فترة واحدة فقط في اليوم
- ⭕ **فترتان**: فترة صباحية وفترة مسائية

### 3️⃣ تحديد عدد الحصص
**خيارات متاحة:**
- ⭕ **حصة واحدة**: حصة واحدة في كل فترة
- ⭕ **حصتان**: حصتان في كل فترة

### 4️⃣ إعداد القاعات
- **عدد القاعات**: من 1 إلى 50 قاعة في كل حصة
- **استخدم الأسهم** لتحديد العدد المطلوب

### 5️⃣ تحديد عدد الحراس
**خيارات متاحة:**
- ⭕ **حارسان**: حارسان في كل قاعة
- ⭕ **ثلاثة حراس**: ثلاثة حراس في كل قاعة

## 🔍 معاينة الإعداد

قبل تطبيق الإعداد، انقر على **"🔍 معاينة الإعداد"** لرؤية:

### 📊 الإحصائيات الإجمالية
- عدد أيام الامتحان
- إجمالي الفترات
- إجمالي الحصص
- إجمالي القاعات
- إجمالي مناصب الحراسة

### 📅 تفاصيل كل يوم
- تفصيل كامل لكل يوم
- الفترات والحصص في كل يوم
- عدد القاعات والحراس المطلوبين

## ✅ تطبيق الإعداد

بعد مراجعة المعاينة:
1. **انقر على "✅ تطبيق الإعداد"**
2. **سيتم إغلاق نافذة الإعداد**
3. **سيتم إعادة إنشاء الواجهة الرئيسية**
4. **ستظهر رسالة تأكيد بالتفاصيل**

## 🗂️ النتيجة النهائية

بعد تطبيق الإعداد ستحصل على:

### 📑 تبويبات الأيام
- **تبويب منفصل لكل يوم امتحان**
- سهولة التنقل بين الأيام
- تنظيم أفضل للعمل

### 🏛️ شبكة القاعات المخصصة
- **عدد القاعات حسب اختيارك**
- **عدد الحراس حسب تحديدك**
- **تنظيم واضح للفترات والحصص**

### 👮 مناصب الحراسة
- **حارس 1، حارس 2** (أو حارس 3 إذا اخترت 3 حراس)
- **مقاعد منفصلة لكل حارس**
- **سهولة التوزيع والتتبع**

## 💡 أمثلة عملية

### مثال 1: امتحان بسيط
```
📅 التواريخ: 2024-01-15, 2024-01-16
⏰ الفترات: فترتان (صباحي ومسائي)
📚 الحصص: حصة واحدة لكل فترة
🏛️ القاعات: 5 قاعات
👮 الحراس: حارسان لكل قاعة

النتيجة: 2 يوم × 2 فترة × 1 حصة × 5 قاعات × 2 حارس = 40 منصب حراسة
```

### مثال 2: امتحان مكثف
```
📅 التواريخ: 2024-01-15, 2024-01-16, 2024-01-17, 2024-01-18
⏰ الفترات: فترتان (صباحي ومسائي)
📚 الحصص: حصتان لكل فترة
🏛️ القاعات: 10 قاعات
👮 الحراس: ثلاثة حراس لكل قاعة

النتيجة: 4 أيام × 2 فترة × 2 حصة × 10 قاعات × 3 حراس = 480 منصب حراسة
```

## ⚠️ نصائح مهمة

### 🎯 التخطيط المسبق
- **احسب عدد الأساتذة المتاحين** قبل الإعداد
- **تأكد من كفاية العدد** لتغطية جميع المناصب
- **خطط لتوزيع الحصص** على الأساتذة

### 🔄 إعادة الإعداد
- **يمكنك تغيير الإعداد** في أي وقت
- **سيتم مسح التوزيع الحالي** عند التغيير
- **احفظ عملك** قبل إعادة الإعداد

### 💾 الحفظ والاستعادة
- **احفظ التوزيع** بانتظام
- **استخدم أسماء ملفات واضحة**
- **احتفظ بنسخ احتياطية**

## 🆘 استكشاف الأخطاء

### "يرجى إدخال تاريخ واحد على الأقل!"
- **تأكد من إدخال تاريخ واحد على الأقل**
- **استخدم تنسيق صحيح للتاريخ**

### "حدث خطأ في المعاينة"
- **تحقق من صحة التواريخ المدخلة**
- **تأكد من اختيار جميع الخيارات**

### "حدث خطأ في تطبيق الإعداد"
- **أعد تشغيل البرنامج**
- **تحقق من صحة الإعدادات**

---

**🎉 مبروك! الآن يمكنك إنشاء جداول امتحان مخصصة بالكامل حسب احتياجاتك الفعلية!**
