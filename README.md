# برنامج توزيع الأساتذة على قاعات الامتحان

## وصف البرنامج
برنامج تفاعلي لتوزيع الأساتذة على قاعات الامتحان بطريقة يدوية باستخدام نظام السحب والإفلات (Drag & Drop). يساعد البرنامج في تنظيم جدول الحراسة مع مراعاة القيود المطلوبة.

## المميزات
- **🔧 إعداد مرن للحصص**: إمكانية تحديد عدد الحصص لكل أستاذ (1-10 حصص)
- **🗃️ قاعدة بيانات متكاملة**: تحميل بيانات الأساتذة من قاعدة البيانات `data.db`
- **📋 بطاقات مفصلة**: عرض الاسم الكامل، رقم التأجير، ومادة التخصص لكل أستاذ
- **📑 تبويبات منظمة**: تبويبات ديناميكية حسب عدد الحصص المحدد
- **🎯 سحب وإفلات محسن**: نظام سحب مرن مع تمييز المقاعد المتاحة والمحظورة
- **📊 جدول الأستاذ التفاعلي**: عرض جدول مفصل لكل أستاذ بعد التعيين يوضح حصصه وأماكنها
- **🎨 تمييز بصري**: ألوان مختلفة للمقاعد المتاحة (أخضر) والمحظورة (أحمر)
- **🛡️ قيود ذكية**: منع تعيين الأستاذ أكثر من مرة في نفس اليوم
- **💾 حفظ وتحميل**: إمكانية حفظ التوزيع وتحميله لاحقاً
- **📈 إحصائيات مفصلة**: عرض تقارير شاملة عن التوزيع
- **🌐 واجهة عربية**: دعم كامل للغة العربية مع تأثيرات بصرية محسنة

## هيكل البرنامج
- **3 أيام**: يومان بفترتين + يوم واحد بفترة واحدة (5 فترات إجمالية)
- **10 قاعات** لكل فترة
- **مقعدان للحراسة** في كل قاعة
- **قاعدة البيانات**: جدول `امتحانات` في ملف `data.db`
- **بيانات الأساتذة**: الاسم الكامل، رقم التأجير، مادة التخصص
- **حصص مرنة**: عدد قابل للتخصيص من الحصص لكل أستاذ (افتراضي: 3)

## كيفية الاستخدام

### 1. تشغيل البرنامج
```bash
python exam_scheduler.py
```

### 2. إعداد عدد الحصص
1. **انقر على زر "⚙️ إعداد عدد الحصص"** في شريط الأدوات
2. **أدخل العدد المطلوب** (من 1 إلى 10 حصص)
3. **سيتم تحديث التبويبات** تلقائياً حسب العدد المحدد

### 3. توزيع الأساتذة
1. **اختيار التبويب**: اختر التبويب المناسب (حصة 1، حصة 2، إلخ...)
2. **مراجعة البطاقة**: كل بطاقة تحتوي على الاسم الكامل، رقم التأجير، ومادة التخصص
3. **سحب البطاقة**: اسحب بطاقة الأستاذ من التبويب
4. **التمييز البصري**: ستظهر المقاعد المتاحة باللون الأخضر والمحظورة بالأحمر
5. **الإفلات**: أفلت البطاقة في المقعد المطلوب
6. **جدول الأستاذ**: سيظهر جدول مفصل للأستاذ يوضح جميع حصصه وأماكنها
7. **التحقق التلقائي**: سيتم التحقق من القيود تلقائياً

### 4. إدارة قاعدة البيانات
1. **انقر على زر "🗃️ معلومات قاعدة البيانات"** لعرض:
   - حالة الاتصال بقاعدة البيانات
   - عدد الأساتذة المحملين
   - عينة من البيانات المحملة
   - إحصائيات الجدول
2. **إعادة تحميل البيانات**: من نافذة معلومات قاعدة البيانات

### 5. إدارة التعيينات
- **عرض جدول الأستاذ**: انقر على أي بطاقة مُعيَّنة لعرض جدول الأستاذ المفصل
- **إزالة تعيين واحد**: انقر على الزر "×" في المقعد
- **إزالة جميع تعيينات أستاذ**: من نافذة جدول الأستاذ
- **مسح الكل**: استخدم زر "مسح الكل" في شريط الأدوات
- **عرض الإحصائيات**: انقر على زر "إحصائيات"

### 4. حفظ وتحميل
- **الحفظ**: انقر على "حفظ التوزيع" لحفظ الجدول الحالي
- **التحميل**: انقر على "تحميل التوزيع" لاستيراد جدول محفوظ

## القيود المطبقة
1. **عدم التكرار**: الأستاذ لا يمكن أن يحرس أكثر من مرة في نفس اليوم
2. **مقعد واحد**: كل مقعد يمكن أن يشغله أستاذ واحد فقط
3. **ثلاث جلسات**: كل أستاذ له 3 جلسات حراسة بالضبط

## الملفات المحفوظة
- يتم حفظ التوزيع في ملفات JSON بصيغة: `schedule_YYYYMMDD_HHMMSS.json`
- تحتوي الملفات على جميع التعيينات مع الطوابع الزمنية

## متطلبات النظام
- Python 3.6 أو أحدث
- نظام التشغيل: Windows, macOS, أو Linux
- لا توجد مكتبات خارجية مطلوبة (يستخدم مكتبات Python المدمجة فقط)

## استكشاف الأخطاء
- **البرنامج لا يبدأ**: تأكد من تثبيت Python بشكل صحيح
- **لا يمكن سحب البطاقة**: تأكد من أن البطاقة غير مُعيَّنة بالفعل
- **رسالة تعارض**: تحقق من عدم وجود تعيين آخر لنفس الأستاذ في نفس اليوم

## المطور
تم تطوير هذا البرنامج باستخدام Python و tkinter لتوفير حل بسيط وفعال لإدارة جداول الحراسة.
