# برنامج توزيع الأساتذة على قاعات الامتحان

## وصف البرنامج
برنامج تفاعلي لتوزيع الأساتذة على قاعات الامتحان بطريقة يدوية باستخدام نظام السحب والإفلات (Drag & Drop). يساعد البرنامج في تنظيم جدول الحراسة مع مراعاة القيود المطلوبة.

## المميزات
- **واجهة تفاعلية**: نظام سحب وإفلات سهل الاستخدام
- **قيود ذكية**: منع تعيين الأستاذ أكثر من مرة في نفس اليوم
- **حفظ وتحميل**: إمكانية حفظ التوزيع وتحميله لاحقاً
- **إحصائيات مفصلة**: عرض تقارير شاملة عن التوزيع
- **واجهة عربية**: دعم كامل للغة العربية

## هيكل البرنامج
- **3 أيام**: يومان بفترتين + يوم واحد بفترة واحدة (5 فترات إجمالية)
- **10 قاعات** لكل فترة
- **مقعدان للحراسة** في كل قاعة
- **20 أستاذ** بأسماء وهمية
- **3 جلسات حراسة** لكل أستاذ

## كيفية الاستخدام

### 1. تشغيل البرنامج
```bash
python exam_scheduler.py
```

### 2. توزيع الأساتذة
1. اختر بطاقة أستاذ من الجانب الأيسر
2. اسحبها إلى المقعد المطلوب في الجدول
3. سيتم التحقق من القيود تلقائياً
4. في حالة وجود تعارض، ستظهر رسالة تحذير

### 3. إدارة التعيينات
- **إزالة تعيين**: انقر على الزر "×" في المقعد
- **مسح الكل**: استخدم زر "مسح الكل" في شريط الأدوات
- **عرض الإحصائيات**: انقر على زر "إحصائيات"

### 4. حفظ وتحميل
- **الحفظ**: انقر على "حفظ التوزيع" لحفظ الجدول الحالي
- **التحميل**: انقر على "تحميل التوزيع" لاستيراد جدول محفوظ

## القيود المطبقة
1. **عدم التكرار**: الأستاذ لا يمكن أن يحرس أكثر من مرة في نفس اليوم
2. **مقعد واحد**: كل مقعد يمكن أن يشغله أستاذ واحد فقط
3. **ثلاث جلسات**: كل أستاذ له 3 جلسات حراسة بالضبط

## الملفات المحفوظة
- يتم حفظ التوزيع في ملفات JSON بصيغة: `schedule_YYYYMMDD_HHMMSS.json`
- تحتوي الملفات على جميع التعيينات مع الطوابع الزمنية

## متطلبات النظام
- Python 3.6 أو أحدث
- نظام التشغيل: Windows, macOS, أو Linux
- لا توجد مكتبات خارجية مطلوبة (يستخدم مكتبات Python المدمجة فقط)

## استكشاف الأخطاء
- **البرنامج لا يبدأ**: تأكد من تثبيت Python بشكل صحيح
- **لا يمكن سحب البطاقة**: تأكد من أن البطاقة غير مُعيَّنة بالفعل
- **رسالة تعارض**: تحقق من عدم وجود تعيين آخر لنفس الأستاذ في نفس اليوم

## المطور
تم تطوير هذا البرنامج باستخدام Python و tkinter لتوفير حل بسيط وفعال لإدارة جداول الحراسة.
