# دليل قاعدة البيانات - برنامج توزيع الأساتذة

## 📋 نظرة عامة

يدعم البرنامج تحميل بيانات الأساتذة من قاعدة البيانات `data.db` الموجودة في نفس مجلد البرنامج.

## 🗃️ هيكل قاعدة البيانات المتوقع

### الجداول المدعومة
البرنامج يبحث عن الجداول التي تحتوي على كلمة:
- `امتحان` (مثل: جدول_الامتحان، امتحانات، إلخ...)
- `أساتذة` (مثل: الأساتذة، جدول_الأساتذة، إلخ...)

### الأعمدة المطلوبة
البرنامج يبحث عن الأعمدة التالية (بأي ترتيب):

#### 🏷️ اسم الأستاذ
- `اسم_الأستاذ`
- `الاسم_الكامل`
- `الاسم`
- `اسم`
- `name`

#### 🔢 رقم التأجير
- `رقم_التأجير`
- `الرمز`
- `رمز`
- `code`
- `id`

#### 📚 مادة التخصص
- `مادة_التخصص`
- `المادة`
- `مادة`
- `التخصص`
- `subject`

## 📊 مثال على هيكل الجدول

```sql
CREATE TABLE جدول_الامتحان (
    المعرف INTEGER PRIMARY KEY,
    اسم_الأستاذ TEXT NOT NULL,
    رقم_التأجير TEXT,
    مادة_التخصص TEXT,
    -- أعمدة أخرى...
);
```

## 🔄 كيفية عمل البرنامج

### 1. البحث عن قاعدة البيانات
- يبحث البرنامج عن ملف `data.db` في نفس المجلد
- إذا لم يجد الملف، يستخدم بيانات وهمية

### 2. استخراج البيانات
- يفحص جميع الجداول الموجودة
- يختار الجدول الذي يحتوي على كلمة "امتحان" أو "أساتذة"
- يستخرج أول 50 سجل من الجدول

### 3. تحويل البيانات
- يحاول مطابقة أسماء الأعمدة مع الحقول المطلوبة
- يحول البيانات إلى التنسيق المطلوب للبرنامج

## ⚠️ ملاحظات مهمة

### البيانات المكررة
- إذا كان نفس الأستاذ موجود عدة مرات في الجدول، سيظهر مرة واحدة فقط
- البرنامج يأخذ أول ظهور للأستاذ

### البيانات الناقصة
- إذا لم يجد البرنامج اسم الأستاذ، يتجاهل السجل
- إذا لم يجد رقم التأجير أو المادة، يضع "غير محدد"

### حد البيانات
- البرنامج يحمل أول 50 سجل فقط لتجنب البطء
- يمكن تعديل هذا الرقم في الكود إذا لزم الأمر

## 🛠️ استكشاف الأخطاء

### "⚠️ ملف قاعدة البيانات غير موجود"
- تأكد من وجود ملف `data.db` في نفس مجلد البرنامج
- تأكد من أن اسم الملف صحيح

### "❌ لم يتم العثور على جدول مناسب"
- تأكد من وجود جدول يحتوي على كلمة "امتحان" أو "أساتذة" في اسمه
- تحقق من أن الجدول يحتوي على بيانات

### "✅ تم تحميل 0 أستاذ من قاعدة البيانات"
- تحقق من أن الجدول يحتوي على عمود للأسماء
- تأكد من أن أسماء الأعمدة تتطابق مع الأسماء المدعومة

## 🔧 تخصيص البرنامج

### تعديل أسماء الأعمدة المدعومة
يمكنك تعديل الوظائف التالية في الكود:
- `extract_teacher_name()` - لأسماء الأساتذة
- `extract_rental_number()` - لأرقام التأجير  
- `extract_subject()` - لمواد التخصص

### تعديل حد البيانات
في الوظيفة `load_teachers_from_db()` غير الرقم 50:
```python
cursor.execute(f"SELECT * FROM `{target_table}` LIMIT 50;")
```

## 📝 مثال عملي

إذا كان لديك جدول بالشكل التالي:
```
| المعرف | اسم_المدرس | الرمز | المادة_المدرسة |
|--------|-------------|-------|----------------|
| 1      | أحمد محمد   | T001  | رياضيات        |
| 2      | فاطمة علي   | T002  | فيزياء         |
```

سيقوم البرنامج بتحويلها إلى:
```
الاسم_الكامل: أحمد محمد
رقم_التأجير: T001  
مادة_التخصص: رياضيات
```

---

**💡 نصيحة**: استخدم أدوات إدارة قواعد البيانات مثل DB Browser for SQLite لفحص وتعديل قاعدة البيانات قبل استخدام البرنامج.
