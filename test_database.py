#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار الاتصال بقاعدة البيانات
"""

import sqlite3
import os

def test_database():
    """اختبار قاعدة البيانات"""
    db_path = "data.db"
    
    print("🔍 اختبار قاعدة البيانات...")
    print("="*50)
    
    # فحص وجود الملف
    if not os.path.exists(db_path):
        print("❌ ملف قاعدة البيانات غير موجود!")
        return False
    
    print(f"✅ ملف قاعدة البيانات موجود: {db_path}")
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # عرض الجداول
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = [table[0] for table in cursor.fetchall()]
        print(f"📋 الجداول الموجودة: {', '.join(tables)}")
        
        # فحص جدول امتحانات
        if "امتحانات" in tables:
            print("\n✅ جدول امتحانات موجود!")
            
            # فحص الأعمدة
            cursor.execute("PRAGMA table_info(امتحانات);")
            columns = cursor.fetchall()
            print("\n📊 أعمدة الجدول:")
            for col in columns:
                print(f"  - {col[1]} ({col[2]})")
            
            # عد السجلات
            cursor.execute("SELECT COUNT(*) FROM امتحانات")
            total_count = cursor.fetchone()[0]
            print(f"\n📈 إجمالي السجلات: {total_count}")
            
            # عد الأساتذة الفريدين
            cursor.execute("""
                SELECT COUNT(DISTINCT الاسم_الكامل) 
                FROM امتحانات 
                WHERE الاسم_الكامل IS NOT NULL 
                AND الاسم_الكامل != ''
                AND TRIM(الاسم_الكامل) != ''
            """)
            unique_teachers = cursor.fetchone()[0]
            print(f"👥 عدد الأساتذة الفريدين: {unique_teachers}")
            
            # عرض عينة من البيانات
            cursor.execute("""
                SELECT DISTINCT الاسم_الكامل, رقم_التأجير, مادة_التخصص 
                FROM امتحانات 
                WHERE الاسم_الكامل IS NOT NULL 
                AND الاسم_الكامل != ''
                AND TRIM(الاسم_الكامل) != ''
                ORDER BY الاسم_الكامل
                LIMIT 10
            """)
            
            sample_data = cursor.fetchall()
            print(f"\n📝 عينة من البيانات ({len(sample_data)} سجل):")
            for i, row in enumerate(sample_data, 1):
                name = row[0] if row[0] else "غير محدد"
                rental = row[1] if row[1] else "غير محدد"
                subject = row[2] if row[2] else "غير محدد"
                print(f"  {i}. {name} | ر.ت: {rental} | {subject}")
            
            print("\n✅ اختبار قاعدة البيانات مكتمل بنجاح!")
            return True
            
        else:
            print("❌ جدول امتحانات غير موجود!")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في الاتصال بقاعدة البيانات: {e}")
        return False
    
    finally:
        if 'conn' in locals():
            conn.close()

if __name__ == "__main__":
    success = test_database()
    if success:
        print("\n🎉 يمكنك الآن تشغيل البرنامج الرئيسي:")
        print("python exam_scheduler.py")
    else:
        print("\n⚠️ يرجى التأكد من وجود قاعدة البيانات وصحة بنيتها")
