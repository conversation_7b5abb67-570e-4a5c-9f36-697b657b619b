# دليل الاستخدام السريع - برنامج توزيع الأساتذة

## 🚀 البدء السريع

### تشغيل البرنامج
```bash
python exam_scheduler.py
```

## 📋 خطوات التوزيع

### 1️⃣ اختيار التبويب
- انقر على التبويب المناسب: **حصة 1** أو **حصة 2** أو **حصة 3**
- كل تبويب يحتوي على جميع الأساتذة لتلك الحصة

### 2️⃣ سحب البطاقة
- مرر الماوس فوق بطاقة الأستاذ (ستتغير للون الفاتح)
- اضغط واسحب البطاقة نحو الجدول
- ستظهر المقاعد المتاحة باللون **الأخضر** 🟢
- ستظهر المقاعد المحظورة باللون **الأحمر** 🔴

### 3️⃣ الإفلات
- أفلت البطاقة في المقعد المطلوب
- إذا كان التعيين صحيحاً، ستظهر نافذة **جدول الأستاذ**

## 📊 جدول الأستاذ

### ماذا يعرض؟
- ✅ **الحصص المُعيَّنة**: تظهر باللون الأخضر مع تفاصيل المكان
- ❌ **الحصص غير المُعيَّنة**: تظهر باللون الأحمر
- 📈 **إحصائيات**: عدد الحصص المُعيَّنة والمتبقية

### الوظائف المتاحة
- **إغلاق**: إغلاق النافذة
- **إلغاء جميع التعيينات**: إزالة جميع حصص هذا الأستاذ

## 🎯 نصائح مهمة

### ✅ ما يُسمح به
- سحب البطاقات غير المُعيَّنة فقط
- تعيين الأستاذ في أيام مختلفة
- عرض جدول أي أستاذ بالنقر على بطاقته المُعيَّنة

### ❌ ما لا يُسمح به
- تعيين الأستاذ أكثر من مرة في نفس اليوم
- وضع أكثر من أستاذ في نفس المقعد
- سحب البطاقات المُعيَّنة (يمكن عرض جدولها فقط)

## 🛠️ أدوات التحكم

### شريط الأدوات السفلي
- **حفظ التوزيع**: حفظ الجدول الحالي في ملف JSON
- **تحميل التوزيع**: استيراد جدول محفوظ
- **مسح الكل**: إزالة جميع التعيينات
- **إحصائيات**: عرض تقرير شامل

### إزالة التعيينات
- **إزالة مقعد واحد**: انقر على "×" في المقعد
- **إزالة جميع حصص أستاذ**: من نافذة جدول الأستاذ
- **مسح الكل**: من شريط الأدوات

## 🎨 الألوان والرموز

| اللون/الرمز | المعنى |
|-------------|---------|
| 🔵 أزرق فاتح | بطاقة متاحة للسحب |
| 🟡 ذهبي | بطاقة قيد السحب |
| 🔘 رمادي | بطاقة مُعيَّنة |
| 🟢 أخضر | مقعد متاح للتعيين |
| 🔴 أحمر | مقعد محظور (تعارض في اليوم) |
| 🟡 أصفر | مقعد مستهدف أثناء السحب |
| ⚪ أبيض | مقعد فارغ |
| 🟢 أخضر فاتح | مقعد مشغول |

## ⚠️ رسائل التحذير

### "لا يمكن تعيين الأستاذ في نفس اليوم"
- **السبب**: الأستاذ لديه حراسة أخرى في نفس اليوم
- **الحل**: اختر يوماً آخر للحصة

### "هذا المقعد مشغول بالفعل"
- **السبب**: يوجد أستاذ آخر في هذا المقعد
- **الحل**: اختر مقعداً آخر في نفس القاعة أو قاعة أخرى

## 💾 حفظ وتحميل البيانات

### تنسيق الملفات
- **الامتداد**: `.json`
- **التسمية**: `schedule_YYYYMMDD_HHMMSS.json`
- **المحتوى**: جميع التعيينات مع الطوابع الزمنية

### نصائح الحفظ
- احفظ نسخة احتياطية قبل إجراء تغييرات كبيرة
- استخدم أسماء وصفية للملفات المحفوظة
- تأكد من حفظ العمل قبل إغلاق البرنامج

---

**💡 نصيحة**: استخدم جدول الأستاذ لمتابعة تقدمك في توزيع الحصص ولمعرفة الحصص المتبقية لكل أستاذ!
