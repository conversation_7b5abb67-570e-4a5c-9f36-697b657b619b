#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
برنامج توزيع الأساتذة على قاعات الامتحان
نظام سحب وإفلات تفاعلي
"""

import tkinter as tk
from tkinter import ttk, messagebox
import json
from datetime import datetime

class TeacherCard:
    """بطاقة الأستاذ القابلة للسحب"""
    def __init__(self, teacher_name, session_id):
        self.teacher_name = teacher_name
        self.session_id = session_id  # معرف الجلسة (1, 2, 3)
        self.is_assigned = False
        self.assigned_day = None
        self.assigned_period = None
        self.assigned_hall = None
        self.assigned_seat = None

class ExamScheduler:
    def __init__(self, root):
        self.root = root
        self.root.title("برنامج توزيع الأساتذة على قاعات الامتحان")
        self.root.geometry("1400x800")
        self.root.configure(bg='#f0f0f0')
        
        # بيانات الأساتذة (أسماء وهمية)
        self.teachers = [
            "د. أحمد محمد", "د. فاطمة علي", "د. محمود حسن", "د. عائشة سالم",
            "د. يوسف إبراهيم", "د. زينب أحمد", "د. عمر خالد", "د. مريم عبدالله",
            "د. حسام الدين", "د. نور الهدى", "د. طارق سعيد", "د. هدى محمد",
            "د. كريم عادل", "د. سارة يوسف", "د. وليد حسام", "د. ليلى أمين",
            "د. رامي صلاح", "د. دينا فؤاد", "د. ماجد عثمان", "د. رنا طاهر"
        ]
        
        # إنشاء بطاقات الأساتذة (3 بطاقات لكل أستاذ)
        self.teacher_cards = []
        for teacher in self.teachers:
            for session in range(1, 4):  # 3 جلسات لكل أستاذ
                card = TeacherCard(teacher, session)
                self.teacher_cards.append(card)
        
        # هيكل الأيام والفترات
        self.schedule_structure = {
            "اليوم الأول": ["الفترة الصباحية", "الفترة المسائية"],
            "اليوم الثاني": ["الفترة الصباحية", "الفترة المسائية"],
            "اليوم الثالث": ["الفترة الصباحية"]
        }
        
        # عدد القاعات والمقاعد
        self.halls_per_period = 10
        self.seats_per_hall = 2
        
        # متغيرات السحب والإفلات
        self.dragged_card = None
        self.drag_start_x = 0
        self.drag_start_y = 0
        
        # إنشاء الواجهة
        self.create_interface()
        
    def create_interface(self):
        """إنشاء واجهة المستخدم"""
        # الإطار الرئيسي
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # عنوان البرنامج
        title_label = tk.Label(main_frame, text="برنامج توزيع الأساتذة على قاعات الامتحان", 
                              font=("Arial", 16, "bold"), bg='#f0f0f0')
        title_label.pack(pady=(0, 20))
        
        # إطار المحتوى الرئيسي
        content_frame = ttk.Frame(main_frame)
        content_frame.pack(fill=tk.BOTH, expand=True)
        
        # الجانب الأيسر: بطاقات الأساتذة
        self.create_teachers_panel(content_frame)
        
        # الجانب الأيمن: جدول الامتحانات
        self.create_schedule_panel(content_frame)
        
        # شريط الأدوات السفلي
        self.create_toolbar(main_frame)
        
    def create_teachers_panel(self, parent):
        """إنشاء لوحة بطاقات الأساتذة"""
        teachers_frame = ttk.LabelFrame(parent, text="بطاقات الأساتذة المتاحة", padding=10)
        teachers_frame.pack(side=tk.LEFT, fill=tk.BOTH, padx=(0, 10))
        
        # إطار قابل للتمرير
        canvas = tk.Canvas(teachers_frame, width=300, bg='white')
        scrollbar = ttk.Scrollbar(teachers_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)
        
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        # إنشاء بطاقات الأساتذة
        self.teacher_widgets = {}
        for i, card in enumerate(self.teacher_cards):
            card_frame = tk.Frame(scrollable_frame, bg='lightblue', relief=tk.RAISED, bd=2)
            card_frame.pack(fill=tk.X, pady=2, padx=5)
            
            # نص البطاقة
            card_text = f"{card.teacher_name}\nالجلسة {card.session_id}"
            card_label = tk.Label(card_frame, text=card_text, bg='lightblue', 
                                 font=("Arial", 9), justify=tk.CENTER)
            card_label.pack(pady=5)
            
            # ربط أحداث السحب
            self.bind_drag_events(card_frame, card)
            self.bind_drag_events(card_label, card)
            
            self.teacher_widgets[card] = card_frame
        
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
    def create_schedule_panel(self, parent):
        """إنشاء لوحة جدول الامتحانات"""
        schedule_frame = ttk.LabelFrame(parent, text="جدول توزيع الحراسة", padding=10)
        schedule_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)
        
        # إطار قابل للتمرير للجدول
        canvas = tk.Canvas(schedule_frame, bg='white')
        h_scrollbar = ttk.Scrollbar(schedule_frame, orient="horizontal", command=canvas.xview)
        v_scrollbar = ttk.Scrollbar(schedule_frame, orient="vertical", command=canvas.yview)
        scrollable_schedule = ttk.Frame(canvas)
        
        scrollable_schedule.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_schedule, anchor="nw")
        canvas.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)
        
        # إنشاء الجدول
        self.create_schedule_grid(scrollable_schedule)
        
        canvas.pack(side="top", fill="both", expand=True)
        h_scrollbar.pack(side="bottom", fill="x")
        v_scrollbar.pack(side="right", fill="y")
        
    def create_schedule_grid(self, parent):
        """إنشاء شبكة الجدول"""
        self.seat_widgets = {}
        
        col = 0
        for day, periods in self.schedule_structure.items():
            # عنوان اليوم
            day_label = tk.Label(parent, text=day, font=("Arial", 12, "bold"), 
                               bg='#4CAF50', fg='white', relief=tk.RAISED, bd=2)
            day_label.grid(row=0, column=col, columnspan=len(periods), sticky="ew", padx=1, pady=1)
            
            period_col = col
            for period in periods:
                # عنوان الفترة
                period_label = tk.Label(parent, text=period, font=("Arial", 10, "bold"), 
                                      bg='#2196F3', fg='white', relief=tk.RAISED, bd=1)
                period_label.grid(row=1, column=period_col, sticky="ew", padx=1, pady=1)
                
                # القاعات والمقاعد
                for hall in range(1, self.halls_per_period + 1):
                    hall_label = tk.Label(parent, text=f"قاعة {hall}", font=("Arial", 9), 
                                        bg='#FFC107', relief=tk.RAISED, bd=1)
                    hall_label.grid(row=hall*2, column=period_col, sticky="ew", padx=1, pady=1)
                    
                    # المقاعد
                    for seat in range(1, self.seats_per_hall + 1):
                        seat_frame = tk.Frame(parent, bg='white', relief=tk.SUNKEN, bd=2, 
                                            width=120, height=40)
                        seat_frame.grid(row=hall*2 + seat, column=period_col, 
                                      sticky="ew", padx=1, pady=1)
                        seat_frame.grid_propagate(False)
                        
                        # تسمية المقعد
                        seat_key = (day, period, hall, seat)
                        self.seat_widgets[seat_key] = seat_frame
                        
                        # ربط أحداث الإفلات
                        self.bind_drop_events(seat_frame, seat_key)
                
                period_col += 1
            col = period_col

    def create_toolbar(self, parent):
        """إنشاء شريط الأدوات"""
        toolbar_frame = ttk.Frame(parent)
        toolbar_frame.pack(fill=tk.X, pady=(10, 0))

        # أزرار التحكم
        ttk.Button(toolbar_frame, text="حفظ التوزيع", command=self.save_schedule).pack(side=tk.LEFT, padx=5)
        ttk.Button(toolbar_frame, text="تحميل التوزيع", command=self.load_schedule).pack(side=tk.LEFT, padx=5)
        ttk.Button(toolbar_frame, text="مسح الكل", command=self.clear_all).pack(side=tk.LEFT, padx=5)
        ttk.Button(toolbar_frame, text="إحصائيات", command=self.show_statistics).pack(side=tk.LEFT, padx=5)

        # معلومات الحالة
        self.status_label = tk.Label(toolbar_frame, text="جاهز للاستخدام",
                                   font=("Arial", 10), fg='green')
        self.status_label.pack(side=tk.RIGHT, padx=5)

    def bind_drag_events(self, widget, card):
        """ربط أحداث السحب للبطاقة"""
        widget.bind("<Button-1>", lambda e: self.start_drag(e, card))
        widget.bind("<B1-Motion>", self.on_drag)
        widget.bind("<ButtonRelease-1>", self.end_drag)

    def bind_drop_events(self, widget, seat_key):
        """ربط أحداث الإفلات للمقعد"""
        widget.bind("<Button-1>", lambda e: self.on_seat_click(e, seat_key))

    def start_drag(self, event, card):
        """بداية عملية السحب"""
        if card.is_assigned:
            return  # لا يمكن سحب البطاقة المُعيَّنة

        self.dragged_card = card
        self.drag_start_x = event.x_root
        self.drag_start_y = event.y_root

        # تغيير مظهر البطاقة أثناء السحب
        widget = self.teacher_widgets[card]
        widget.configure(bg='yellow', relief=tk.RAISED, bd=3)

        self.update_status(f"جاري سحب: {card.teacher_name} - الجلسة {card.session_id}")

    def on_drag(self, event):
        """أثناء عملية السحب"""
        if self.dragged_card is None:
            return

        # يمكن إضافة تأثيرات بصرية هنا
        pass

    def end_drag(self, event):
        """نهاية عملية السحب"""
        if self.dragged_card is None:
            return

        # العثور على المقعد المستهدف
        target_widget = event.widget.winfo_containing(event.x_root, event.y_root)
        target_seat = self.find_seat_by_widget(target_widget)

        if target_seat:
            self.assign_teacher_to_seat(self.dragged_card, target_seat)

        # إعادة تعيين مظهر البطاقة
        if not self.dragged_card.is_assigned:
            widget = self.teacher_widgets[self.dragged_card]
            widget.configure(bg='lightblue', relief=tk.RAISED, bd=2)

        self.dragged_card = None
        self.update_status("جاهز للاستخدام")

    def find_seat_by_widget(self, widget):
        """العثور على المقعد بواسطة الويدجت"""
        for seat_key, seat_widget in self.seat_widgets.items():
            if seat_widget == widget or widget in seat_widget.winfo_children():
                return seat_key
        return None

    def assign_teacher_to_seat(self, card, seat_key):
        """تعيين الأستاذ للمقعد"""
        day, period, hall, seat = seat_key

        # التحقق من القيود
        if not self.check_assignment_constraints(card, day):
            messagebox.showerror("خطأ في التوزيع",
                               f"لا يمكن تعيين {card.teacher_name} في {day}\n"
                               f"الأستاذ لديه حراسة أخرى في نفس اليوم!")
            return False

        # التحقق من أن المقعد فارغ
        if self.is_seat_occupied(seat_key):
            messagebox.showwarning("تحذير", "هذا المقعد مشغول بالفعل!")
            return False

        # تعيين البطاقة
        card.is_assigned = True
        card.assigned_day = day
        card.assigned_period = period
        card.assigned_hall = hall
        card.assigned_seat = seat

        # تحديث الواجهة
        self.update_seat_display(seat_key, card)
        self.update_teacher_card_display(card)

        self.update_status(f"تم تعيين {card.teacher_name} في {day} - {period} - قاعة {hall} - مقعد {seat}")
        return True

    def check_assignment_constraints(self, card, target_day):
        """التحقق من قيود التوزيع"""
        # التحقق من عدم وجود حراسة أخرى في نفس اليوم
        for other_card in self.teacher_cards:
            if (other_card.teacher_name == card.teacher_name and
                other_card.is_assigned and
                other_card.assigned_day == target_day):
                return False
        return True

    def is_seat_occupied(self, seat_key):
        """التحقق من أن المقعد مشغول"""
        for card in self.teacher_cards:
            if (card.is_assigned and
                card.assigned_day == seat_key[0] and
                card.assigned_period == seat_key[1] and
                card.assigned_hall == seat_key[2] and
                card.assigned_seat == seat_key[3]):
                return True
        return False

    def update_seat_display(self, seat_key, card):
        """تحديث عرض المقعد"""
        seat_widget = self.seat_widgets[seat_key]

        # مسح المحتوى السابق
        for child in seat_widget.winfo_children():
            child.destroy()

        # إضافة معلومات الأستاذ
        seat_widget.configure(bg='lightgreen')
        info_label = tk.Label(seat_widget, text=f"{card.teacher_name}\nج{card.session_id}",
                            font=("Arial", 8), bg='lightgreen', wraplength=100)
        info_label.pack(expand=True)

        # إضافة زر الحذف
        remove_btn = tk.Button(seat_widget, text="×", font=("Arial", 8),
                             command=lambda: self.remove_assignment(card),
                             bg='red', fg='white', width=2, height=1)
        remove_btn.pack(side=tk.BOTTOM, anchor=tk.SE)

    def update_teacher_card_display(self, card):
        """تحديث عرض بطاقة الأستاذ"""
        widget = self.teacher_widgets[card]
        if card.is_assigned:
            widget.configure(bg='lightgray')
            # إضافة معلومات التعيين
            for child in widget.winfo_children():
                if isinstance(child, tk.Label):
                    child.configure(text=f"{card.teacher_name}\nالجلسة {card.session_id}\n"
                                        f"({card.assigned_day})")
        else:
            widget.configure(bg='lightblue')

    def remove_assignment(self, card):
        """إزالة تعيين الأستاذ"""
        if not card.is_assigned:
            return

        # الحصول على معلومات المقعد
        seat_key = (card.assigned_day, card.assigned_period,
                   card.assigned_hall, card.assigned_seat)

        # إعادة تعيين البطاقة
        card.is_assigned = False
        card.assigned_day = None
        card.assigned_period = None
        card.assigned_hall = None
        card.assigned_seat = None

        # تحديث الواجهة
        self.clear_seat_display(seat_key)
        self.update_teacher_card_display(card)

        self.update_status(f"تم إلغاء تعيين {card.teacher_name}")

    def clear_seat_display(self, seat_key):
        """مسح عرض المقعد"""
        seat_widget = self.seat_widgets[seat_key]
        seat_widget.configure(bg='white')

        # مسح المحتوى
        for child in seat_widget.winfo_children():
            child.destroy()

    def on_seat_click(self, event, seat_key):
        """عند النقر على المقعد"""
        if self.dragged_card:
            self.assign_teacher_to_seat(self.dragged_card, seat_key)

    def update_status(self, message):
        """تحديث رسالة الحالة"""
        self.status_label.configure(text=message)
        self.root.update_idletasks()

    def save_schedule(self):
        """حفظ التوزيع في ملف"""
        try:
            schedule_data = {
                'assignments': [],
                'timestamp': datetime.now().isoformat()
            }

            for card in self.teacher_cards:
                if card.is_assigned:
                    assignment = {
                        'teacher_name': card.teacher_name,
                        'session_id': card.session_id,
                        'day': card.assigned_day,
                        'period': card.assigned_period,
                        'hall': card.assigned_hall,
                        'seat': card.assigned_seat
                    }
                    schedule_data['assignments'].append(assignment)

            filename = f"schedule_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(schedule_data, f, ensure_ascii=False, indent=2)

            messagebox.showinfo("نجح الحفظ", f"تم حفظ التوزيع في الملف:\n{filename}")
            self.update_status(f"تم حفظ التوزيع في {filename}")

        except Exception as e:
            messagebox.showerror("خطأ في الحفظ", f"حدث خطأ أثناء حفظ الملف:\n{str(e)}")

    def load_schedule(self):
        """تحميل التوزيع من ملف"""
        from tkinter import filedialog

        try:
            filename = filedialog.askopenfilename(
                title="اختر ملف التوزيع",
                filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
            )

            if not filename:
                return

            with open(filename, 'r', encoding='utf-8') as f:
                schedule_data = json.load(f)

            # مسح التوزيع الحالي
            self.clear_all()

            # تحميل التعيينات
            for assignment in schedule_data.get('assignments', []):
                # العثور على البطاقة المناسبة
                card = self.find_teacher_card(assignment['teacher_name'],
                                            assignment['session_id'])
                if card:
                    seat_key = (assignment['day'], assignment['period'],
                              assignment['hall'], assignment['seat'])
                    self.assign_teacher_to_seat(card, seat_key)

            messagebox.showinfo("نجح التحميل", "تم تحميل التوزيع بنجاح!")
            self.update_status("تم تحميل التوزيع")

        except Exception as e:
            messagebox.showerror("خطأ في التحميل", f"حدث خطأ أثناء تحميل الملف:\n{str(e)}")

    def find_teacher_card(self, teacher_name, session_id):
        """العثور على بطاقة الأستاذ"""
        for card in self.teacher_cards:
            if card.teacher_name == teacher_name and card.session_id == session_id:
                return card
        return None

    def clear_all(self):
        """مسح جميع التعيينات"""
        if messagebox.askyesno("تأكيد المسح", "هل أنت متأكد من مسح جميع التعيينات؟"):
            for card in self.teacher_cards:
                if card.is_assigned:
                    self.remove_assignment(card)

            self.update_status("تم مسح جميع التعيينات")

    def show_statistics(self):
        """عرض إحصائيات التوزيع"""
        # حساب الإحصائيات
        total_cards = len(self.teacher_cards)
        assigned_cards = sum(1 for card in self.teacher_cards if card.is_assigned)
        unassigned_cards = total_cards - assigned_cards

        # إحصائيات الأساتذة
        teacher_stats = {}
        for card in self.teacher_cards:
            if card.teacher_name not in teacher_stats:
                teacher_stats[card.teacher_name] = {'total': 0, 'assigned': 0}
            teacher_stats[card.teacher_name]['total'] += 1
            if card.is_assigned:
                teacher_stats[card.teacher_name]['assigned'] += 1

        # إحصائيات الأيام
        day_stats = {}
        for day in self.schedule_structure.keys():
            day_stats[day] = sum(1 for card in self.teacher_cards
                               if card.is_assigned and card.assigned_day == day)

        # إنشاء نافذة الإحصائيات
        stats_window = tk.Toplevel(self.root)
        stats_window.title("إحصائيات التوزيع")
        stats_window.geometry("600x500")
        stats_window.configure(bg='#f0f0f0')

        # إطار قابل للتمرير
        canvas = tk.Canvas(stats_window, bg='white')
        scrollbar = ttk.Scrollbar(stats_window, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        # المحتوى
        content = f"""
إحصائيات التوزيع العامة:
{'='*50}
إجمالي البطاقات: {total_cards}
البطاقات المُعيَّنة: {assigned_cards}
البطاقات غير المُعيَّنة: {unassigned_cards}
نسبة الإنجاز: {(assigned_cards/total_cards)*100:.1f}%

إحصائيات الأساتذة:
{'='*50}
"""

        for teacher, stats in teacher_stats.items():
            content += f"{teacher}: {stats['assigned']}/{stats['total']} جلسات\n"

        content += f"\nإحصائيات الأيام:\n{'='*50}\n"
        for day, count in day_stats.items():
            content += f"{day}: {count} حراسة\n"

        # عرض النص
        text_widget = tk.Text(scrollable_frame, wrap=tk.WORD, font=("Arial", 11),
                            bg='white', fg='black', padx=20, pady=20)
        text_widget.insert(tk.END, content)
        text_widget.configure(state=tk.DISABLED)
        text_widget.pack(fill=tk.BOTH, expand=True)

        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")


def main():
    """الدالة الرئيسية"""
    root = tk.Tk()
    app = ExamScheduler(root)
    root.mainloop()


if __name__ == "__main__":
    main()
