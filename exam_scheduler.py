#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
برنامج توزيع الأساتذة على قاعات الامتحان
نظام سحب وإفلات تفاعلي مع قاعدة بيانات
"""

import tkinter as tk
from tkinter import ttk, messagebox, simpledialog
import json
import sqlite3
import os
from datetime import datetime

class TeacherCard:
    """بطاقة الأستاذ القابلة للسحب"""
    def __init__(self, teacher_data, session_id):
        self.teacher_data = teacher_data  # قاموس يحتوي على بيانات الأستاذ
        self.teacher_name = teacher_data.get("الاسم_الكامل", "غير محدد")
        self.rental_number = teacher_data.get("رقم_التأجير", "غير محدد")
        self.subject = teacher_data.get("مادة_التخصص", "غير محدد")
        self.session_id = session_id  # معرف الجلسة (1, 2, 3, ...)
        self.is_assigned = False
        self.assigned_day = None
        self.assigned_period = None
        self.assigned_hall = None
        self.assigned_seat = None

class ExamScheduler:
    def __init__(self, root):
        self.root = root
        self.root.title("برنامج توزيع الأساتذة على قاعات الامتحان")
        self.root.geometry("1400x800")
        self.root.configure(bg='#f0f0f0')

        # إعدادات البرنامج
        self.sessions_per_teacher = 3  # افتراضي
        self.db_path = "data.db"

        # بيانات الأساتذة من قاعدة البيانات
        self.teachers_data = []
        self.load_teachers_from_db()

        # إذا لم تكن هناك بيانات في قاعدة البيانات، استخدم بيانات وهمية
        if not self.teachers_data:
            self.teachers_data = [
                {"الاسم_الكامل": "د. أحمد محمد علي", "رقم_التأجير": "T001", "مادة_التخصص": "الرياضيات"},
                {"الاسم_الكامل": "د. فاطمة سالم", "رقم_التأجير": "T002", "مادة_التخصص": "الفيزياء"},
                {"الاسم_الكامل": "د. محمود حسن", "رقم_التأجير": "T003", "مادة_التخصص": "الكيمياء"},
                {"الاسم_الكامل": "د. عائشة أحمد", "رقم_التأجير": "T004", "مادة_التخصص": "الأحياء"},
                {"الاسم_الكامل": "د. يوسف إبراهيم", "رقم_التأجير": "T005", "مادة_التخصص": "اللغة العربية"},
                {"الاسم_الكامل": "د. زينب خالد", "رقم_التأجير": "T006", "مادة_التخصص": "اللغة الإنجليزية"},
                {"الاسم_الكامل": "د. عمر سعيد", "رقم_التأجير": "T007", "مادة_التخصص": "التاريخ"},
                {"الاسم_الكامل": "د. مريم عبدالله", "رقم_التأجير": "T008", "مادة_التخصص": "الجغرافيا"},
                {"الاسم_الكامل": "د. حسام الدين", "رقم_التأجير": "T009", "مادة_التخصص": "الفلسفة"},
                {"الاسم_الكامل": "د. نور الهدى", "رقم_التأجير": "T010", "مادة_التخصص": "علوم الحاسوب"}
            ]
        
        # إنشاء بطاقات الأساتذة
        self.teacher_cards = []
        self.create_teacher_cards()
        
        # هيكل الأيام والفترات
        self.schedule_structure = {
            "اليوم الأول": ["الفترة الصباحية", "الفترة المسائية"],
            "اليوم الثاني": ["الفترة الصباحية", "الفترة المسائية"],
            "اليوم الثالث": ["الفترة الصباحية"]
        }
        
        # عدد القاعات والمقاعد
        self.halls_per_period = 10
        self.seats_per_hall = 2
        
        # متغيرات السحب والإفلات
        self.dragged_card = None
        self.drag_start_x = 0
        self.drag_start_y = 0
        
        # إنشاء الواجهة
        self.create_interface()

    def load_teachers_from_db(self):
        """تحميل بيانات الأساتذة من قاعدة البيانات"""
        if not os.path.exists(self.db_path):
            print("⚠️ ملف قاعدة البيانات غير موجود")
            return

        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # البحث عن جدول الامتحانات أو الأساتذة
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
            tables = [table[0] for table in cursor.fetchall()]

            # محاولة العثور على جدول مناسب
            target_table = None
            for table in tables:
                if 'امتحان' in table or 'أساتذة' in table:
                    target_table = table
                    break

            if target_table:
                # الحصول على هيكل الجدول
                cursor.execute(f"PRAGMA table_info(`{target_table}`);")
                columns = [col[1] for col in cursor.fetchall()]

                # محاولة استخراج البيانات
                cursor.execute(f"SELECT * FROM `{target_table}` LIMIT 50;")
                rows = cursor.fetchall()

                # تحويل البيانات إلى تنسيق مناسب
                for row in rows:
                    teacher_data = {}
                    for i, col in enumerate(columns):
                        if i < len(row):
                            teacher_data[col] = row[i]

                    # محاولة استخراج المعلومات المطلوبة
                    name = self.extract_teacher_name(teacher_data)
                    rental_num = self.extract_rental_number(teacher_data)
                    subject = self.extract_subject(teacher_data)

                    if name:  # إذا تم العثور على اسم
                        self.teachers_data.append({
                            "الاسم_الكامل": name,
                            "رقم_التأجير": rental_num or "غير محدد",
                            "مادة_التخصص": subject or "غير محدد"
                        })

            conn.close()
            print(f"✅ تم تحميل {len(self.teachers_data)} أستاذ من قاعدة البيانات")

        except Exception as e:
            print(f"❌ خطأ في تحميل قاعدة البيانات: {e}")

    def extract_teacher_name(self, data):
        """استخراج اسم الأستاذ من البيانات"""
        name_fields = ['اسم_الأستاذ', 'الاسم_الكامل', 'الاسم', 'اسم', 'name']
        for field in name_fields:
            if field in data and data[field]:
                return str(data[field]).strip()
        return None

    def extract_rental_number(self, data):
        """استخراج رقم التأجير من البيانات"""
        rental_fields = ['رقم_التأجير', 'الرمز', 'رمز', 'code', 'id']
        for field in rental_fields:
            if field in data and data[field]:
                return str(data[field]).strip()
        return None

    def extract_subject(self, data):
        """استخراج مادة التخصص من البيانات"""
        subject_fields = ['مادة_التخصص', 'المادة', 'مادة', 'التخصص', 'subject']
        for field in subject_fields:
            if field in data and data[field]:
                return str(data[field]).strip()
        return None

    def create_teacher_cards(self):
        """إنشاء بطاقات الأساتذة"""
        self.teacher_cards = []
        for teacher_data in self.teachers_data:
            for session in range(1, self.sessions_per_teacher + 1):
                card = TeacherCard(teacher_data, session)
                self.teacher_cards.append(card)

    def ask_sessions_count(self):
        """سؤال المستخدم عن عدد الحصص لكل أستاذ"""
        from tkinter import simpledialog

        sessions = simpledialog.askinteger(
            "إعداد عدد الحصص",
            "كم عدد الحصص التي سيقوم كل أستاذ بحراستها؟",
            initialvalue=self.sessions_per_teacher,
            minvalue=1,
            maxvalue=10
        )

        if sessions and sessions != self.sessions_per_teacher:
            self.sessions_per_teacher = sessions
            # إعادة إنشاء البطاقات
            self.create_teacher_cards()
            # إعادة إنشاء الواجهة
            self.refresh_interface()
            messagebox.showinfo("تم التحديث", f"تم تحديث عدد الحصص إلى {sessions} حصص لكل أستاذ")

    def refresh_interface(self):
        """تحديث الواجهة بعد تغيير الإعدادات"""
        # مسح الواجهة الحالية
        for widget in self.root.winfo_children():
            widget.destroy()

        # إعادة إنشاء الواجهة
        self.create_interface()
        
    def create_interface(self):
        """إنشاء واجهة المستخدم"""
        # الإطار الرئيسي
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # عنوان البرنامج
        title_label = tk.Label(main_frame, text="برنامج توزيع الأساتذة على قاعات الامتحان", 
                              font=("Arial", 16, "bold"), bg='#f0f0f0')
        title_label.pack(pady=(0, 20))
        
        # إطار المحتوى الرئيسي
        content_frame = ttk.Frame(main_frame)
        content_frame.pack(fill=tk.BOTH, expand=True)
        
        # الجانب الأيسر: بطاقات الأساتذة
        self.create_teachers_panel(content_frame)
        
        # الجانب الأيمن: جدول الامتحانات
        self.create_schedule_panel(content_frame)
        
        # شريط الأدوات السفلي
        self.create_toolbar(main_frame)
        
    def create_teachers_panel(self, parent):
        """إنشاء لوحة بطاقات الأساتذة مع تبويبات"""
        teachers_frame = ttk.LabelFrame(parent, text="بطاقات الأساتذة المتاحة", padding=10)
        teachers_frame.pack(side=tk.LEFT, fill=tk.BOTH, padx=(0, 10))

        # إنشاء التبويبات
        self.notebook = ttk.Notebook(teachers_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True)

        # إنشاء تبويب لكل حصة
        self.session_tabs = {}
        self.teacher_widgets = {}

        for session_num in range(1, self.sessions_per_teacher + 1):
            # إنشاء التبويب
            tab_frame = ttk.Frame(self.notebook)
            self.notebook.add(tab_frame, text=f"الحصة {session_num}")

            # إطار قابل للتمرير داخل التبويب
            canvas = tk.Canvas(tab_frame, bg='white')
            scrollbar = ttk.Scrollbar(tab_frame, orient="vertical", command=canvas.yview)
            scrollable_frame = ttk.Frame(canvas)

            scrollable_frame.bind(
                "<Configure>",
                lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
            )

            canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
            canvas.configure(yscrollcommand=scrollbar.set)

            # إضافة بطاقات الأساتذة لهذه الحصة
            session_cards = [card for card in self.teacher_cards if card.session_id == session_num]

            for card in session_cards:
                card_frame = tk.Frame(scrollable_frame, bg='lightblue', relief=tk.RAISED, bd=2,
                                    cursor='hand2')
                card_frame.pack(fill=tk.X, pady=3, padx=5)

                # نص البطاقة مع المعلومات المفصلة
                card_text = f"{card.teacher_name}\nر.ت: {card.rental_number}\n{card.subject}"
                card_label = tk.Label(card_frame, text=card_text, bg='lightblue',
                                     font=("Arial", 9), justify=tk.CENTER,
                                     cursor='hand2', wraplength=120)
                card_label.pack(pady=5)

                # ربط أحداث السحب المحسنة
                self.bind_enhanced_drag_events(card_frame, card)
                self.bind_enhanced_drag_events(card_label, card)

                self.teacher_widgets[card] = card_frame

            canvas.pack(side="left", fill="both", expand=True)
            scrollbar.pack(side="right", fill="y")

            self.session_tabs[session_num] = {
                'frame': tab_frame,
                'canvas': canvas,
                'scrollable_frame': scrollable_frame
            }
        
    def create_schedule_panel(self, parent):
        """إنشاء لوحة جدول الامتحانات"""
        schedule_frame = ttk.LabelFrame(parent, text="جدول توزيع الحراسة", padding=10)
        schedule_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)
        
        # إطار قابل للتمرير للجدول
        canvas = tk.Canvas(schedule_frame, bg='white')
        h_scrollbar = ttk.Scrollbar(schedule_frame, orient="horizontal", command=canvas.xview)
        v_scrollbar = ttk.Scrollbar(schedule_frame, orient="vertical", command=canvas.yview)
        scrollable_schedule = ttk.Frame(canvas)
        
        scrollable_schedule.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_schedule, anchor="nw")
        canvas.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)
        
        # إنشاء الجدول
        self.create_schedule_grid(scrollable_schedule)
        
        canvas.pack(side="top", fill="both", expand=True)
        h_scrollbar.pack(side="bottom", fill="x")
        v_scrollbar.pack(side="right", fill="y")
        
    def create_schedule_grid(self, parent):
        """إنشاء شبكة الجدول"""
        self.seat_widgets = {}
        
        col = 0
        for day, periods in self.schedule_structure.items():
            # عنوان اليوم
            day_label = tk.Label(parent, text=day, font=("Arial", 12, "bold"), 
                               bg='#4CAF50', fg='white', relief=tk.RAISED, bd=2)
            day_label.grid(row=0, column=col, columnspan=len(periods), sticky="ew", padx=1, pady=1)
            
            period_col = col
            for period in periods:
                # عنوان الفترة
                period_label = tk.Label(parent, text=period, font=("Arial", 10, "bold"), 
                                      bg='#2196F3', fg='white', relief=tk.RAISED, bd=1)
                period_label.grid(row=1, column=period_col, sticky="ew", padx=1, pady=1)
                
                # القاعات والمقاعد
                for hall in range(1, self.halls_per_period + 1):
                    hall_label = tk.Label(parent, text=f"قاعة {hall}", font=("Arial", 9), 
                                        bg='#FFC107', relief=tk.RAISED, bd=1)
                    hall_label.grid(row=hall*2, column=period_col, sticky="ew", padx=1, pady=1)
                    
                    # المقاعد
                    for seat in range(1, self.seats_per_hall + 1):
                        seat_frame = tk.Frame(parent, bg='white', relief=tk.SUNKEN, bd=2, 
                                            width=120, height=40)
                        seat_frame.grid(row=hall*2 + seat, column=period_col, 
                                      sticky="ew", padx=1, pady=1)
                        seat_frame.grid_propagate(False)
                        
                        # تسمية المقعد
                        seat_key = (day, period, hall, seat)
                        self.seat_widgets[seat_key] = seat_frame
                        
                        # ربط أحداث الإفلات
                        self.bind_drop_events(seat_frame, seat_key)
                
                period_col += 1
            col = period_col

    def create_toolbar(self, parent):
        """إنشاء شريط الأدوات"""
        toolbar_frame = ttk.Frame(parent)
        toolbar_frame.pack(fill=tk.X, pady=(10, 0))

        # أزرار التحكم
        ttk.Button(toolbar_frame, text="⚙️ إعداد عدد الحصص",
                  command=self.ask_sessions_count).pack(side=tk.LEFT, padx=5)
        ttk.Separator(toolbar_frame, orient='vertical').pack(side=tk.LEFT, padx=5, fill=tk.Y)
        ttk.Button(toolbar_frame, text="💾 حفظ التوزيع", command=self.save_schedule).pack(side=tk.LEFT, padx=5)
        ttk.Button(toolbar_frame, text="📂 تحميل التوزيع", command=self.load_schedule).pack(side=tk.LEFT, padx=5)
        ttk.Button(toolbar_frame, text="🗑️ مسح الكل", command=self.clear_all).pack(side=tk.LEFT, padx=5)
        ttk.Button(toolbar_frame, text="📊 إحصائيات", command=self.show_statistics).pack(side=tk.LEFT, padx=5)

        # معلومات الحالة
        self.status_label = tk.Label(toolbar_frame, text=f"جاهز للاستخدام | عدد الحصص: {self.sessions_per_teacher}",
                                   font=("Arial", 10), fg='green')
        self.status_label.pack(side=tk.RIGHT, padx=5)

    def bind_enhanced_drag_events(self, widget, card):
        """ربط أحداث السحب المحسنة للبطاقة"""
        widget.bind("<Button-1>", lambda e: self.start_enhanced_drag(e, card))
        widget.bind("<B1-Motion>", lambda e: self.on_enhanced_drag(e, card))
        widget.bind("<ButtonRelease-1>", lambda e: self.end_enhanced_drag(e, card))
        widget.bind("<Enter>", lambda e: self.on_card_hover(e, card, True))
        widget.bind("<Leave>", lambda e: self.on_card_hover(e, card, False))

    def bind_drop_events(self, widget, seat_key):
        """ربط أحداث الإفلات للمقعد"""
        widget.bind("<Button-1>", lambda e: self.on_seat_click(e, seat_key))

    def on_card_hover(self, event, card, entering):
        """عند تمرير الماوس فوق البطاقة"""
        if card.is_assigned:
            return

        widget = self.teacher_widgets[card]
        if entering:
            widget.configure(bg='lightcyan', relief=tk.RAISED, bd=3)
            self.update_status(f"بطاقة: {card.teacher_name} - الحصة {card.session_id}")
        else:
            widget.configure(bg='lightblue', relief=tk.RAISED, bd=2)
            self.update_status("جاهز للاستخدام")

    def start_enhanced_drag(self, event, card):
        """بداية عملية السحب المحسنة"""
        if card.is_assigned:
            # إذا كانت البطاقة مُعيَّنة، اعرض جدول الأستاذ
            self.show_teacher_schedule(card.teacher_name)
            return

        self.dragged_card = card
        self.drag_start_x = event.x_root
        self.drag_start_y = event.y_root

        # تغيير مظهر البطاقة أثناء السحب
        widget = self.teacher_widgets[card]
        widget.configure(bg='gold', relief=tk.RAISED, bd=4, cursor='crosshair')

        # تمييز المقاعد المتاحة
        self.highlight_available_seats(card)

        self.update_status(f"جاري سحب: {card.teacher_name} - الحصة {card.session_id}")

    def on_enhanced_drag(self, event, card):
        """أثناء عملية السحب المحسنة"""
        if self.dragged_card is None:
            return

        # تحديث موقع المؤشر
        widget = self.teacher_widgets[card]
        widget.configure(cursor='crosshair')

        # العثور على المقعد تحت المؤشر وتمييزه
        target_widget = self.root.winfo_containing(event.x_root, event.y_root)
        self.highlight_target_seat(target_widget)

    def end_enhanced_drag(self, event, card):
        """نهاية عملية السحب المحسنة"""
        if self.dragged_card is None:
            return

        # العثور على المقعد المستهدف
        target_widget = self.root.winfo_containing(event.x_root, event.y_root)
        target_seat = self.find_seat_by_widget(target_widget)

        success = False
        if target_seat:
            success = self.assign_teacher_to_seat(self.dragged_card, target_seat)

        # إعادة تعيين مظهر البطاقة
        widget = self.teacher_widgets[self.dragged_card]
        if not success:
            widget.configure(bg='lightblue', relief=tk.RAISED, bd=2, cursor='hand2')

        # إزالة التمييز من جميع المقاعد
        self.clear_seat_highlights()

        self.dragged_card = None
        self.update_status("جاهز للاستخدام")

    def highlight_available_seats(self, card):
        """تمييز المقاعد المتاحة للبطاقة"""
        for seat_key, seat_widget in self.seat_widgets.items():
            day = seat_key[0]
            if self.check_assignment_constraints(card, day) and not self.is_seat_occupied(seat_key):
                seat_widget.configure(bg='lightgreen', relief=tk.RAISED, bd=2)
            elif not self.check_assignment_constraints(card, day):
                seat_widget.configure(bg='lightcoral', relief=tk.SUNKEN, bd=1)

    def highlight_target_seat(self, target_widget):
        """تمييز المقعد المستهدف"""
        # إعادة تعيين جميع المقاعد أولاً
        for seat_key, seat_widget in self.seat_widgets.items():
            if not self.is_seat_occupied(seat_key):
                day = seat_key[0]
                if self.check_assignment_constraints(self.dragged_card, day):
                    seat_widget.configure(bg='lightgreen')
                else:
                    seat_widget.configure(bg='lightcoral')

        # تمييز المقعد المستهدف
        target_seat = self.find_seat_by_widget(target_widget)
        if target_seat and target_seat in self.seat_widgets:
            seat_widget = self.seat_widgets[target_seat]
            if not self.is_seat_occupied(target_seat):
                seat_widget.configure(bg='yellow', relief=tk.RAISED, bd=3)

    def clear_seat_highlights(self):
        """إزالة التمييز من جميع المقاعد"""
        for seat_key, seat_widget in self.seat_widgets.items():
            if not self.is_seat_occupied(seat_key):
                seat_widget.configure(bg='white', relief=tk.SUNKEN, bd=2)

    def find_seat_by_widget(self, widget):
        """العثور على المقعد بواسطة الويدجت"""
        for seat_key, seat_widget in self.seat_widgets.items():
            if seat_widget == widget or widget in seat_widget.winfo_children():
                return seat_key
        return None

    def assign_teacher_to_seat(self, card, seat_key):
        """تعيين الأستاذ للمقعد"""
        day, period, hall, seat = seat_key

        # التحقق من القيود
        if not self.check_assignment_constraints(card, day):
            messagebox.showerror("خطأ في التوزيع",
                               f"لا يمكن تعيين {card.teacher_name} في {day}\n"
                               f"الأستاذ لديه حراسة أخرى في نفس اليوم!")
            return False

        # التحقق من أن المقعد فارغ
        if self.is_seat_occupied(seat_key):
            messagebox.showwarning("تحذير", "هذا المقعد مشغول بالفعل!")
            return False

        # تعيين البطاقة
        card.is_assigned = True
        card.assigned_day = day
        card.assigned_period = period
        card.assigned_hall = hall
        card.assigned_seat = seat

        # تحديث الواجهة
        self.update_seat_display(seat_key, card)
        self.update_teacher_card_display(card)

        # عرض جدول الأستاذ بعد التعيين
        self.show_teacher_schedule(card.teacher_name)

        self.update_status(f"تم تعيين {card.teacher_name} في {day} - {period} - قاعة {hall} - مقعد {seat}")
        return True

    def check_assignment_constraints(self, card, target_day):
        """التحقق من قيود التوزيع"""
        # التحقق من عدم وجود حراسة أخرى في نفس اليوم
        for other_card in self.teacher_cards:
            if (other_card.teacher_name == card.teacher_name and
                other_card.is_assigned and
                other_card.assigned_day == target_day):
                return False
        return True

    def is_seat_occupied(self, seat_key):
        """التحقق من أن المقعد مشغول"""
        for card in self.teacher_cards:
            if (card.is_assigned and
                card.assigned_day == seat_key[0] and
                card.assigned_period == seat_key[1] and
                card.assigned_hall == seat_key[2] and
                card.assigned_seat == seat_key[3]):
                return True
        return False

    def update_seat_display(self, seat_key, card):
        """تحديث عرض المقعد"""
        seat_widget = self.seat_widgets[seat_key]

        # مسح المحتوى السابق
        for child in seat_widget.winfo_children():
            child.destroy()

        # إضافة معلومات الأستاذ
        seat_widget.configure(bg='lightgreen')
        info_label = tk.Label(seat_widget, text=f"{card.teacher_name}\nج{card.session_id}",
                            font=("Arial", 8), bg='lightgreen', wraplength=100)
        info_label.pack(expand=True)

        # إضافة زر الحذف
        remove_btn = tk.Button(seat_widget, text="×", font=("Arial", 8),
                             command=lambda: self.remove_assignment(card),
                             bg='red', fg='white', width=2, height=1)
        remove_btn.pack(side=tk.BOTTOM, anchor=tk.SE)

    def update_teacher_card_display(self, card):
        """تحديث عرض بطاقة الأستاذ"""
        widget = self.teacher_widgets[card]
        if card.is_assigned:
            widget.configure(bg='lightgray')
            # إضافة معلومات التعيين
            for child in widget.winfo_children():
                if isinstance(child, tk.Label):
                    child.configure(text=f"{card.teacher_name}\nالجلسة {card.session_id}\n"
                                        f"({card.assigned_day})")
        else:
            widget.configure(bg='lightblue')

    def remove_assignment(self, card):
        """إزالة تعيين الأستاذ"""
        if not card.is_assigned:
            return

        # الحصول على معلومات المقعد
        seat_key = (card.assigned_day, card.assigned_period,
                   card.assigned_hall, card.assigned_seat)

        # إعادة تعيين البطاقة
        card.is_assigned = False
        card.assigned_day = None
        card.assigned_period = None
        card.assigned_hall = None
        card.assigned_seat = None

        # تحديث الواجهة
        self.clear_seat_display(seat_key)
        self.update_teacher_card_display(card)

        self.update_status(f"تم إلغاء تعيين {card.teacher_name}")

    def clear_seat_display(self, seat_key):
        """مسح عرض المقعد"""
        seat_widget = self.seat_widgets[seat_key]
        seat_widget.configure(bg='white')

        # مسح المحتوى
        for child in seat_widget.winfo_children():
            child.destroy()

    def on_seat_click(self, event, seat_key):
        """عند النقر على المقعد"""
        if self.dragged_card:
            self.assign_teacher_to_seat(self.dragged_card, seat_key)

    def update_status(self, message):
        """تحديث رسالة الحالة"""
        if message == "جاهز للاستخدام":
            message = f"جاهز للاستخدام | عدد الحصص: {self.sessions_per_teacher}"
        self.status_label.configure(text=message)
        self.root.update_idletasks()

    def save_schedule(self):
        """حفظ التوزيع في ملف"""
        try:
            schedule_data = {
                'assignments': [],
                'timestamp': datetime.now().isoformat()
            }

            for card in self.teacher_cards:
                if card.is_assigned:
                    assignment = {
                        'teacher_name': card.teacher_name,
                        'session_id': card.session_id,
                        'day': card.assigned_day,
                        'period': card.assigned_period,
                        'hall': card.assigned_hall,
                        'seat': card.assigned_seat
                    }
                    schedule_data['assignments'].append(assignment)

            filename = f"schedule_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(schedule_data, f, ensure_ascii=False, indent=2)

            messagebox.showinfo("نجح الحفظ", f"تم حفظ التوزيع في الملف:\n{filename}")
            self.update_status(f"تم حفظ التوزيع في {filename}")

        except Exception as e:
            messagebox.showerror("خطأ في الحفظ", f"حدث خطأ أثناء حفظ الملف:\n{str(e)}")

    def load_schedule(self):
        """تحميل التوزيع من ملف"""
        from tkinter import filedialog

        try:
            filename = filedialog.askopenfilename(
                title="اختر ملف التوزيع",
                filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
            )

            if not filename:
                return

            with open(filename, 'r', encoding='utf-8') as f:
                schedule_data = json.load(f)

            # مسح التوزيع الحالي
            self.clear_all()

            # تحميل التعيينات
            for assignment in schedule_data.get('assignments', []):
                # العثور على البطاقة المناسبة
                card = self.find_teacher_card(assignment['teacher_name'],
                                            assignment['session_id'])
                if card:
                    seat_key = (assignment['day'], assignment['period'],
                              assignment['hall'], assignment['seat'])
                    self.assign_teacher_to_seat(card, seat_key)

            messagebox.showinfo("نجح التحميل", "تم تحميل التوزيع بنجاح!")
            self.update_status("تم تحميل التوزيع")

        except Exception as e:
            messagebox.showerror("خطأ في التحميل", f"حدث خطأ أثناء تحميل الملف:\n{str(e)}")

    def find_teacher_card(self, teacher_name, session_id):
        """العثور على بطاقة الأستاذ"""
        for card in self.teacher_cards:
            if card.teacher_name == teacher_name and card.session_id == session_id:
                return card
        return None

    def clear_all(self):
        """مسح جميع التعيينات"""
        if messagebox.askyesno("تأكيد المسح", "هل أنت متأكد من مسح جميع التعيينات؟"):
            for card in self.teacher_cards:
                if card.is_assigned:
                    self.remove_assignment(card)

            self.update_status("تم مسح جميع التعيينات")

    def show_statistics(self):
        """عرض إحصائيات التوزيع"""
        # حساب الإحصائيات
        total_cards = len(self.teacher_cards)
        assigned_cards = sum(1 for card in self.teacher_cards if card.is_assigned)
        unassigned_cards = total_cards - assigned_cards

        # إحصائيات الأساتذة
        teacher_stats = {}
        for card in self.teacher_cards:
            if card.teacher_name not in teacher_stats:
                teacher_stats[card.teacher_name] = {'total': 0, 'assigned': 0}
            teacher_stats[card.teacher_name]['total'] += 1
            if card.is_assigned:
                teacher_stats[card.teacher_name]['assigned'] += 1

        # إحصائيات الأيام
        day_stats = {}
        for day in self.schedule_structure.keys():
            day_stats[day] = sum(1 for card in self.teacher_cards
                               if card.is_assigned and card.assigned_day == day)

        # إنشاء نافذة الإحصائيات
        stats_window = tk.Toplevel(self.root)
        stats_window.title("إحصائيات التوزيع")
        stats_window.geometry("600x500")
        stats_window.configure(bg='#f0f0f0')

        # إطار قابل للتمرير
        canvas = tk.Canvas(stats_window, bg='white')
        scrollbar = ttk.Scrollbar(stats_window, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        # المحتوى
        content = f"""
إحصائيات التوزيع العامة:
{'='*50}
إجمالي البطاقات: {total_cards}
البطاقات المُعيَّنة: {assigned_cards}
البطاقات غير المُعيَّنة: {unassigned_cards}
نسبة الإنجاز: {(assigned_cards/total_cards)*100:.1f}%

إحصائيات الأساتذة:
{'='*50}
"""

        for teacher, stats in teacher_stats.items():
            content += f"{teacher}: {stats['assigned']}/{stats['total']} جلسات\n"

        content += f"\nإحصائيات الأيام:\n{'='*50}\n"
        for day, count in day_stats.items():
            content += f"{day}: {count} حراسة\n"

        # عرض النص
        text_widget = tk.Text(scrollable_frame, wrap=tk.WORD, font=("Arial", 11),
                            bg='white', fg='black', padx=20, pady=20)
        text_widget.insert(tk.END, content)
        text_widget.configure(state=tk.DISABLED)
        text_widget.pack(fill=tk.BOTH, expand=True)

        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

    def show_teacher_schedule(self, teacher_name):
        """عرض جدول الأستاذ المفصل"""
        # جمع معلومات حصص الأستاذ
        teacher_assignments = []
        for card in self.teacher_cards:
            if card.teacher_name == teacher_name:
                if card.is_assigned:
                    teacher_assignments.append({
                        'session': card.session_id,
                        'day': card.assigned_day,
                        'period': card.assigned_period,
                        'hall': card.assigned_hall,
                        'seat': card.assigned_seat,
                        'assigned': True
                    })
                else:
                    teacher_assignments.append({
                        'session': card.session_id,
                        'assigned': False
                    })

        # ترتيب الحصص حسب رقم الجلسة
        teacher_assignments.sort(key=lambda x: x['session'])

        # إنشاء نافذة جدول الأستاذ
        schedule_window = tk.Toplevel(self.root)
        schedule_window.title(f"جدول الأستاذ: {teacher_name}")
        schedule_window.geometry("500x400")
        schedule_window.configure(bg='#f0f0f0')

        # جعل النافذة في المقدمة
        schedule_window.transient(self.root)
        schedule_window.grab_set()

        # عنوان النافذة
        title_label = tk.Label(schedule_window, text=f"جدول حصص الأستاذ: {teacher_name}",
                              font=("Arial", 14, "bold"), bg='#f0f0f0', fg='#2c3e50')
        title_label.pack(pady=10)

        # إطار الجدول
        table_frame = ttk.Frame(schedule_window)
        table_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)

        # إنشاء الجدول
        columns = ('الحصة', 'الحالة', 'اليوم', 'الفترة', 'القاعة', 'المقعد')
        tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=8)

        # تعريف العناوين
        for col in columns:
            tree.heading(col, text=col)
            tree.column(col, width=80, anchor='center')

        # إضافة البيانات
        for assignment in teacher_assignments:
            if assignment['assigned']:
                values = (
                    f"الحصة {assignment['session']}",
                    "مُعيَّنة ✓",
                    assignment['day'],
                    assignment['period'],
                    f"قاعة {assignment['hall']}",
                    f"مقعد {assignment['seat']}"
                )
                tree.insert('', tk.END, values=values, tags=('assigned',))
            else:
                values = (
                    f"الحصة {assignment['session']}",
                    "غير مُعيَّنة ✗",
                    "-",
                    "-",
                    "-",
                    "-"
                )
                tree.insert('', tk.END, values=values, tags=('unassigned',))

        # تنسيق الألوان
        tree.tag_configure('assigned', background='lightgreen', foreground='darkgreen')
        tree.tag_configure('unassigned', background='lightcoral', foreground='darkred')

        # شريط التمرير
        scrollbar_tree = ttk.Scrollbar(table_frame, orient="vertical", command=tree.yview)
        tree.configure(yscrollcommand=scrollbar_tree.set)

        tree.pack(side="left", fill="both", expand=True)
        scrollbar_tree.pack(side="right", fill="y")

        # معلومات إضافية
        info_frame = ttk.Frame(schedule_window)
        info_frame.pack(fill=tk.X, padx=20, pady=10)

        assigned_count = sum(1 for a in teacher_assignments if a['assigned'])
        total_count = len(teacher_assignments)
        remaining_count = total_count - assigned_count

        info_text = f"الحصص المُعيَّنة: {assigned_count} من {total_count} | المتبقية: {remaining_count}"
        info_label = tk.Label(info_frame, text=info_text, font=("Arial", 11),
                             bg='#f0f0f0', fg='#34495e')
        info_label.pack()

        # نصائح للحصص المتبقية
        if remaining_count > 0:
            tips_label = tk.Label(info_frame,
                                text="💡 نصيحة: يمكنك سحب الحصص المتبقية من التبويبات إلى المقاعد المتاحة",
                                font=("Arial", 10), bg='#f0f0f0', fg='#7f8c8d', wraplength=450)
            tips_label.pack(pady=5)

        # أزرار التحكم
        buttons_frame = ttk.Frame(schedule_window)
        buttons_frame.pack(fill=tk.X, padx=20, pady=10)

        ttk.Button(buttons_frame, text="إغلاق",
                  command=schedule_window.destroy).pack(side=tk.RIGHT, padx=5)

        if assigned_count > 0:
            ttk.Button(buttons_frame, text="إلغاء جميع تعيينات هذا الأستاذ",
                      command=lambda: self.remove_all_teacher_assignments(teacher_name, schedule_window)).pack(side=tk.LEFT, padx=5)

        # توسيط النافذة
        schedule_window.update_idletasks()
        x = (schedule_window.winfo_screenwidth() // 2) - (schedule_window.winfo_width() // 2)
        y = (schedule_window.winfo_screenheight() // 2) - (schedule_window.winfo_height() // 2)
        schedule_window.geometry(f"+{x}+{y}")

    def remove_all_teacher_assignments(self, teacher_name, window):
        """إزالة جميع تعيينات الأستاذ"""
        if messagebox.askyesno("تأكيد الإزالة",
                              f"هل أنت متأكد من إلغاء جميع تعيينات الأستاذ {teacher_name}؟"):
            for card in self.teacher_cards:
                if card.teacher_name == teacher_name and card.is_assigned:
                    self.remove_assignment(card)

            window.destroy()
            self.update_status(f"تم إلغاء جميع تعيينات الأستاذ {teacher_name}")


def main():
    """الدالة الرئيسية"""
    root = tk.Tk()
    app = ExamScheduler(root)
    root.mainloop()


if __name__ == "__main__":
    main()
